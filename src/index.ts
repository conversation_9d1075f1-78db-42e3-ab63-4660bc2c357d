// 使用 Wrangler 生成的运行时类型，无需导入
// 类型已在 worker-configuration.d.ts 中定义

// 扩展 Env 接口以包含 secrets（不会在 wrangler.jsonc 中显示）
interface ExtendedEnv extends Env {
    MANUAL_TRIGGER_KEY: string;
    LARK_WEBHOOK_URL: string;
}

interface AppConfig {
    package_name: string;
    app_name: string;
}

interface PlayStoreResponse {
    status: 'available' | 'unavailable';
    title?: string;
    appId?: string;
    developer?: {
        devId?: string;
    };
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    playstoreUrl?: string;
    description?: string;
    installs?: string;
    minInstalls?: number;
    maxInstalls?: number;
	released?: string;
}

interface AppStatusRecord {
    package_name: string;
    title?: string;
    appId?: string;
    devId?: string;
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    isAlive: number;
    lastCheck: string;
    belongUs: number;
    downTime?: string | null;
    released?: string;
}

interface AppStatusRecord {
    package_name: string;
    title?: string;
    appId?: string;
    devId?: string;
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    isAlive: number;
    lastCheck: string;
    belongUs: number;
    downTime?: string | null;
    released?: string;
}

interface WeeklySummary {
    newApps: AppSummaryInfo[];
    takenDownApps: AppSummaryInfo[];
    restoredApps: AppSummaryInfo[];
    totalNewApps: number;
    totalTakenDown: number;
    totalRestored: number;
}

interface AppSummaryInfo {
    package_name: string;
    app_name: string;
    title?: string;
    released?: string;
    downTime?: string;
    daysOnline?: number;
}

export default {
    async fetch(request: Request, env: ExtendedEnv, ctx: ExecutionContext): Promise<Response> {
        const url = new URL(request.url);

        // 添加健康检查端点
        if (url.pathname === '/health') {
            try {
                const result = await env.DB.prepare("SELECT 1 AS status").first();
                return Response.json({ status: 'ok', db: result?.status === 1 });
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Unknown error';
                return Response.json({ status: 'error', message: errorMessage }, { status: 500 });
            }
        }

        if (url.pathname === '/trigger') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            ctx.waitUntil(executeMonitor(env));
            return new Response('监测任务已启动');
        }

        // 新增管理接口
        if (url.pathname === '/manage') {
            const auth = request.headers.get('Authorization');
            if (auth !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            // 处理应用列表管理
            if (request.method === 'GET') {
                const apps = await getAllTrackedApps(env);
                return Response.json(apps);
            }

            if (request.method === 'POST') {
                const body = await request.json<AppConfig>();
                await addTrackedApp(env, body);
                return new Response('应用已添加');
            }

            if (request.method === 'DELETE') {
                const { package_name } = await request.json<{ package_name: string }>();
                await removeTrackedApp(env, package_name);
                return new Response('应用已移除');
            }
        }

        // 添加测试端点
        if (url.pathname === '/test-lark') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            try {
                await sendLarkAlert(env,
                    { package_name: 'test.package', app_name: 'Test App' },
                    '这是一条测试消息\n包含多行内容\n用于测试消息发送');
                return new Response('Test message sent successfully');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                return new Response(`Failed to send test message: ${errorMessage}`, { status: 500 });
            }
        }

        // 添加周汇总测试端点
        if (url.pathname === '/test-weekly-summary') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            try {
                const summary = await getWeeklySummary(env);
                await sendWeeklySummaryAlert(env, summary);
                return new Response('Weekly summary sent successfully');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                return new Response(`Failed to send weekly summary: ${errorMessage}`, { status: 500 });
            }
        }

        // 添加单个应用检查测试端点
        if (url.pathname === '/test-app-check') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            const packageName = url.searchParams.get('package');
            if (!packageName) {
                return new Response('Missing package parameter', { status: 400 });
            }

            try {
                // 查找应用配置
                const appConfig = await env.APPS_STORE_ID_TMP.get<AppConfig>(packageName, 'json');
                if (!appConfig) {
                    return new Response(`App ${packageName} not found in tracking list`, { status: 404 });
                }

                console.log(`[测试检查] 开始检查应用: ${appConfig.app_name} (${packageName})`);

                // 执行检查
                await checkAppWithBackoff(appConfig, env);

                return new Response(`App check completed for ${appConfig.app_name}. Check logs for details.`);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                console.error(`[测试检查] 失败:`, errorMessage);
                return new Response(`Failed to check app: ${errorMessage}`, { status: 500 });
            }
        }

        return new Response(`管理端点:
GET /manage - 获取跟踪应用列表
POST /manage - 添加应用 {package_name, app_name}
DELETE /manage - 移除应用 {package_name}
GET /test-lark - 测试飞书通知
GET /test-weekly-summary - 测试周汇总通知`);
    },

    async scheduled(_event: ScheduledEvent, env: ExtendedEnv, ctx: ExecutionContext): Promise<void> {
        ctx.waitUntil(executeMonitor(env));
    }
};

// 安全执行D1数据库操作（优化版）
async function d1SafeRun(query: D1PreparedStatement, logErrors: boolean = true): Promise<D1Result> {
    try {
        const result = await query.run();

        if (!result.success) {
            throw new Error(result.error || 'D1 操作失败');
        }

        return result;
    } catch (err) {
        if (logErrors) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            console.error('数据库操作失败:', errorMessage);
        }
        throw err;
    }
}

// 安全截断字段到指定长度
function truncateField(value: string | null | undefined, maxLength: number): string | null {
    return value ? value.substring(0, maxLength) : null;
}

// 应用列表管理函数（优化版）
async function getAllTrackedApps(env: ExtendedEnv): Promise<AppConfig[]> {
    const list = await env.APPS_STORE_ID_TMP.list();
    const apps: AppConfig[] = [];

    for (const key of list.keys) {
        const app = await env.APPS_STORE_ID_TMP.get<AppConfig>(key.name, 'json');
        if (app) apps.push(app);
    }

    return apps;
}

async function addTrackedApp(env: ExtendedEnv, app: AppConfig) {
    // 存储应用到APPS_STORE
    await env.APPS_STORE_ID_TMP.put(app.package_name, JSON.stringify(app));

    const now = new Date().toISOString();
    try {
        const details = await checkPlayStore(env.API_KEY, app.package_name);

        // 确定初始状态
        const isAlive = details.status === 'available';

        // 准备要插入的数据字段，使用 truncateField 确保长度
        const insertData = {
            package_name: app.package_name,
            title: truncateField(details.title, 255),
            appId: truncateField(details.appId, 100),
            devId: truncateField(details.developer?.devId, 100),
            developerId: truncateField(details.developerId, 100),
            developerEmail: truncateField(details.developerEmail, 255),
            developerWebsite: truncateField(details.developerWebsite, 255),
            developerLegalName: truncateField(details.developerLegalName, 255),
            developerLegalEmail: truncateField(details.developerLegalEmail, 255),
            developerLegalAddress: truncateField(details.developerLegalAddress, 500),
            developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
            developerInternalID: truncateField(details.developerInternalID, 100),
            isAlive: isAlive ? 1 : 0,
            lastCheck: now,
            downTime: isAlive ? null : now,
            released: truncateField(details.released, 50)
        };

        // 构建SQL插入语句（为新应用设置belongUs默认值0）
        const insertQuery = env.DB.prepare(`
            INSERT INTO app_status (
                package_name, title, appId, devId, developerId,
                developerEmail, developerWebsite, developerLegalName,
                developerLegalEmail, developerLegalAddress, developerLegalPhoneNumber,
                developerInternalID, isAlive, lastCheck, belongUs, downTime, released
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
            insertData.package_name,
            insertData.title,
            insertData.appId,
            insertData.devId,
            insertData.developerId,
            insertData.developerEmail,
            insertData.developerWebsite,
            insertData.developerLegalName,
            insertData.developerLegalEmail,
            insertData.developerLegalAddress,
            insertData.developerLegalPhoneNumber,
            insertData.developerInternalID,
            insertData.isAlive,
            insertData.lastCheck,
            0, // belongUs 默认值
            insertData.downTime,
			insertData.released
        );

        await d1SafeRun(insertQuery, false);
        console.log(`应用 ${app.app_name} 添加成功`);

    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error(`添加应用 ${app.app_name} 失败: ${errorMessage}`);

        // 回退插入基本记录
        try {
            const fallbackInsert = env.DB.prepare(`
                INSERT INTO app_status (package_name, isAlive, lastCheck, belongUs)
                VALUES (?, ?, ?, ?)
            `).bind(app.package_name, 1, now, 0);

            await d1SafeRun(fallbackInsert, false);
        } catch (fallbackErr) {
            console.error(`回退插入失败: ${app.package_name}`);
        }
    }
}

async function removeTrackedApp(env: ExtendedEnv, package_name: string) {
    await env.APPS_STORE_ID_TMP.delete(package_name);
}

// 全局监控锁，防止重叠执行
let isMonitorRunning = false;

// 监控核心逻辑（优化版）
async function executeMonitor(env: ExtendedEnv) {
    // 检查是否已有监控任务在运行
    if (isMonitorRunning) {
        console.log('监控任务已在运行中，跳过本次执行');
        return;
    }

    isMonitorRunning = true;
    const startTime = Date.now();

    try {
        // 检查是否需要发送周汇总
        await checkAndSendWeeklySummary(env);

        const apps = await getAllTrackedApps(env);
        console.log(`开始监控 ${apps.length} 个应用`);

        let successCount = 0;
        let errorCount = 0;

        for (const app of apps) {
            try {
                await checkAppWithBackoff(app, env);
                successCount++;
                // 适当间隔，确保单次监控在4分钟内完成 (120个应用约需4分钟)
                await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (err) {
                errorCount++;
                console.error(`检查 ${app.app_name} 失败:`, err instanceof Error ? err.message : err);
            }
        }

        const duration = Math.round((Date.now() - startTime) / 1000);
        console.log(`监控完成: 成功 ${successCount}, 失败 ${errorCount}, 耗时 ${duration}秒`);
    } finally {
        isMonitorRunning = false;
    }
}

async function checkAppWithBackoff(app: AppConfig, env: ExtendedEnv) {
    const now = new Date().toISOString();
    let details: PlayStoreResponse;

    try {
        // 获取应用详情
        details = await checkPlayStore(env.API_KEY, app.package_name);
        const currentStatus = details.status === 'available';

        // 查询当前状态
        const query = env.DB.prepare("SELECT isAlive, downTime FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query, false);

        const prevRecord = result.results[0] as { isAlive: number; downTime?: string | null } | null;
        const prevAlive = prevRecord ? Boolean(prevRecord.isAlive) : true;

        // 添加详细日志
        console.log(`[状态检查] ${app.app_name}:`);
        console.log(`  - 数据库记录存在: ${!!prevRecord}`);
        console.log(`  - 数据库中isAlive: ${prevRecord?.isAlive}`);
        console.log(`  - 数据库中downTime: ${prevRecord?.downTime}`);
        console.log(`  - 解析后prevAlive: ${prevAlive}`);
        console.log(`  - API检查结果: ${details.status}`);
        console.log(`  - 解析后currentStatus: ${currentStatus}`);
        console.log(`  - 状态是否变化: ${prevAlive !== currentStatus}`);

        // 只在状态变化时输出日志
        if (prevAlive !== currentStatus) {
            console.log(`[状态变化] ${app.app_name}: ${prevAlive ? '在架' : '下架'} -> ${currentStatus ? '在架' : '下架'}`);
        }

        // 确定下架时间
        let downTime = prevRecord?.downTime || null;
        if (prevAlive && !currentStatus) {
            downTime = now; // 刚下架
        }
        // 注意：当应用恢复上架时，我们保留原有的downTime，不清除它
        // 这样可以保持下架历史记录，用于统计和显示

        // 根据是否存在记录和应用状态选择更新策略
        let dbQuery;
        if (prevRecord) {
            // 存在记录，只更新必要字段
            if (currentStatus) {
                // 应用在架时，更新详细信息（避免覆盖下架时的数据）
                const data = {
                    title: truncateField(details.title, 255),
                    appId: truncateField(details.appId, 100),
                    devId: truncateField(details.developer?.devId, 100),
                    developerId: truncateField(details.developerId, 100),
                    developerEmail: truncateField(details.developerEmail, 255),
                    developerWebsite: truncateField(details.developerWebsite, 255),
                    developerLegalName: truncateField(details.developerLegalName, 255),
                    developerLegalEmail: truncateField(details.developerLegalEmail, 255),
                    developerLegalAddress: truncateField(details.developerLegalAddress, 500),
                    developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
                    developerInternalID: truncateField(details.developerInternalID, 100),
                    released: truncateField(details.released, 50)
                };

                dbQuery = env.DB.prepare(`
                    UPDATE app_status SET
                        title = ?, appId = ?, devId = ?, developerId = ?,
                        developerEmail = ?, developerWebsite = ?, developerLegalName = ?,
                        developerLegalEmail = ?, developerLegalAddress = ?, developerLegalPhoneNumber = ?,
                        developerInternalID = ?, released = ?,
                        isAlive = ?, lastCheck = ?, downTime = ?
                    WHERE package_name = ?
                `).bind(
                    data.title, data.appId, data.devId, data.developerId,
                    data.developerEmail, data.developerWebsite, data.developerLegalName,
                    data.developerLegalEmail, data.developerLegalAddress, data.developerLegalPhoneNumber,
                    data.developerInternalID, data.released,
                    1, now, downTime, app.package_name
                );
            } else {
                // 应用下架时，只更新状态相关字段，保留原有数据
                dbQuery = env.DB.prepare(`
                    UPDATE app_status SET
                        isAlive = ?, lastCheck = ?, downTime = ?
                    WHERE package_name = ?
                `).bind(0, now, downTime, app.package_name);
            }
        } else {
            // 不存在记录，执行插入
            if (currentStatus) {
                // 应用在架时插入完整数据
                const data = {
                    title: truncateField(details.title, 255),
                    appId: truncateField(details.appId, 100),
                    devId: truncateField(details.developer?.devId, 100),
                    developerId: truncateField(details.developerId, 100),
                    developerEmail: truncateField(details.developerEmail, 255),
                    developerWebsite: truncateField(details.developerWebsite, 255),
                    developerLegalName: truncateField(details.developerLegalName, 255),
                    developerLegalEmail: truncateField(details.developerLegalEmail, 255),
                    developerLegalAddress: truncateField(details.developerLegalAddress, 500),
                    developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
                    developerInternalID: truncateField(details.developerInternalID, 100),
                    released: truncateField(details.released, 50)
                };

                dbQuery = env.DB.prepare(`
                    INSERT INTO app_status (
                        package_name, title, appId, devId, developerId,
                        developerEmail, developerWebsite, developerLegalName,
                        developerLegalEmail, developerLegalAddress, developerLegalPhoneNumber,
                        developerInternalID, isAlive, lastCheck, belongUs, downTime, released
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `).bind(
                    app.package_name, data.title, data.appId, data.devId, data.developerId,
                    data.developerEmail, data.developerWebsite, data.developerLegalName,
                    data.developerLegalEmail, data.developerLegalAddress, data.developerLegalPhoneNumber,
                    data.developerInternalID, 1, now, 0, downTime, data.released
                );
            } else {
                // 应用下架时只插入基本信息（不设置belongUs，保持数据库默认值）
                dbQuery = env.DB.prepare(`
                    INSERT INTO app_status (package_name, isAlive, lastCheck, downTime)
                    VALUES (?, ?, ?, ?)
                `).bind(app.package_name, 0, now, downTime);
            }
        }

        // 执行数据库操作
        await d1SafeRun(dbQuery, false);

        // 处理状态变化通知
        if (prevAlive !== currentStatus) {
            const alertType = currentStatus ? 'active' : 'taken_down';
            console.log(`[发送通知] ${app.app_name}: 状态变化通知类型 = ${alertType}`);
            console.log(`[发送通知] downTime参数: ${downTime}`);

            await sendStatusChangeAlert(
                env,
                app,
                alertType,
                details,
                downTime
            );

            console.log(`[发送通知] ${app.app_name}: 通知发送完成`);
        }
		// 检查是否是新应用（数据库无记录且应用在架）
		else if (!prevRecord && currentStatus) {
            console.log(`[发送通知] ${app.app_name}: 新应用上架通知`);
			await sendStatusChangeAlert(
				env,
				app,
				'new_active',
				details,
				null
			);
		} else {
            console.log(`[跳过通知] ${app.app_name}: 状态无变化或其他原因`);
        }

    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error(`检查 ${app.app_name} 失败: ${errorMessage}`);

        // 静默更新最后检查时间
        try {
            const updateQuery = env.DB.prepare(`
                UPDATE app_status SET lastCheck = ? WHERE package_name = ?
            `).bind(now, app.package_name);
            await d1SafeRun(updateQuery, false);
        } catch (updateErr) {
            // 静默处理更新错误
        }
    }
}

function formatToBeiJingTime(isoString: string): string {
    const date = new Date(isoString);
    return new Date(date.getTime())
        .toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
}

// 解析 released 字段并计算在架天数
function parseReleasedDate(releasedStr: string): Date | null {
    if (!releasedStr) return null;

    try {
        // 处理格式如 "Apr 24, 2025" 的日期字符串
        const date = new Date(releasedStr);

        // 验证日期是否有效
        if (isNaN(date.getTime())) {
            console.warn(`无法解析发布日期: ${releasedStr}`);
            return null;
        }

        return date;
    } catch (error) {
        console.warn(`解析发布日期时出错: ${releasedStr}`, error);
        return null;
    }
}

// 计算在架天数
function calculateDaysOnline(releasedDate: Date, downTime: string): number {
    try {
        const downDate = new Date(downTime);
        const timeDiff = downDate.getTime() - releasedDate.getTime();
        const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

        // 确保天数不为负数
        return Math.max(0, daysDiff);
    } catch (error) {
        console.warn('计算在架天数时出错:', error);
        return 0;
    }
}

// 检查是否为北京时间周一早上9点
function isMondayMorning9AM(): boolean {
    const now = new Date();
    const beijingTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));

    // 检查是否为周一（0=周日, 1=周一）
    const isMonday = beijingTime.getDay() === 1;

    // 检查是否为早上9点（8:55-9:05之间都认为是9点，避免定时任务时间偏差）
    const hour = beijingTime.getHours();
    const minute = beijingTime.getMinutes();
    const is9AM = hour === 9 || (hour === 8 && minute >= 55) || (hour === 9 && minute <= 5);

    return isMonday && is9AM;
}

// 获取上周的日期范围（北京时间）
function getLastWeekRange(): { start: string, end: string } {
    const now = new Date();
    const beijingTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));

    // 获取当前周一的日期
    const currentMonday = new Date(beijingTime);
    currentMonday.setDate(beijingTime.getDate() - beijingTime.getDay() + 1);
    currentMonday.setHours(0, 0, 0, 0);

    // 上周一
    const lastMonday = new Date(currentMonday);
    lastMonday.setDate(currentMonday.getDate() - 7);

    // 上周日
    const lastSunday = new Date(currentMonday);
    lastSunday.setDate(currentMonday.getDate() - 1);
    lastSunday.setHours(23, 59, 59, 999);

    return {
        start: lastMonday.toISOString(),
        end: lastSunday.toISOString()
    };
}

// 查询上周的应用状态变化汇总
async function getWeeklySummary(env: ExtendedEnv): Promise<WeeklySummary> {
    const { start, end } = getLastWeekRange();
    const trackedApps = await getAllTrackedApps(env);

    // 创建应用名称映射
    const appNameMap = new Map<string, string>();
    trackedApps.forEach(app => {
        appNameMap.set(app.package_name, app.app_name);
    });

    const summary: WeeklySummary = {
        newApps: [],
        takenDownApps: [],
        restoredApps: [],
        totalNewApps: 0,
        totalTakenDown: 0,
        totalRestored: 0
    };

    try {
        // 查询上周新上架的应用（released字段解析后的日期在上周范围内）
        const allAppsQuery = env.DB.prepare(`
            SELECT package_name, title, released, downTime, isAlive
            FROM app_status
            WHERE released IS NOT NULL
        `);

        const allAppsResult = await d1SafeRun(allAppsQuery);

        // 处理新上架应用
        for (const row of allAppsResult.results as any[]) {
            if (row.released) {
                const releasedDate = parseReleasedDate(row.released);
                if (releasedDate) {
                    const releasedTime = releasedDate.getTime();
                    const startTime = new Date(start).getTime();
                    const endTime = new Date(end).getTime();

                    // 检查是否在上周范围内新上架
                    if (releasedTime >= startTime && releasedTime <= endTime) {
                        const appName = appNameMap.get(row.package_name) || row.package_name;
                        summary.newApps.push({
                            package_name: row.package_name,
                            app_name: appName,
                            title: row.title,
                            released: row.released
                        });
                    }
                }
            }
        }

        // 查询上周下架的应用（downTime在上周范围内）
        const takenDownQuery = env.DB.prepare(`
            SELECT package_name, title, released, downTime
            FROM app_status
            WHERE downTime >= ? AND downTime <= ?
        `).bind(start, end);

        const takenDownResult = await d1SafeRun(takenDownQuery);

        for (const row of takenDownResult.results as any[]) {
            const appName = appNameMap.get(row.package_name) || row.package_name;
            let daysOnline = undefined;

            // 计算在架天数
            if (row.released && row.downTime) {
                const releasedDate = parseReleasedDate(row.released);
                if (releasedDate) {
                    daysOnline = calculateDaysOnline(releasedDate, row.downTime);
                }
            }

            summary.takenDownApps.push({
                package_name: row.package_name,
                app_name: appName,
                title: row.title,
                released: row.released,
                downTime: row.downTime,
                daysOnline
            });
        }

        // 查询恢复上架的应用
        // 判断逻辑：下架时间在上周范围内，但当前状态是在架
        // 这表示应用在上周下架后又恢复了上架
        const restoredQuery = env.DB.prepare(`
            SELECT package_name, title, released, downTime
            FROM app_status
            WHERE downTime >= ? AND downTime <= ?
            AND isAlive = 1
        `).bind(start, end);

        const restoredResult = await d1SafeRun(restoredQuery);

        for (const row of restoredResult.results as any[]) {
            const appName = appNameMap.get(row.package_name) || row.package_name;
            summary.restoredApps.push({
                package_name: row.package_name,
                app_name: appName,
                title: row.title,
                released: row.released,
                downTime: row.downTime
            });
        }

        // 设置统计数量
        summary.totalNewApps = summary.newApps.length;
        summary.totalTakenDown = summary.takenDownApps.length;
        summary.totalRestored = summary.restoredApps.length;

    } catch (error) {
        console.error('查询周汇总数据失败:', error);
    }

    return summary;
}

// 发送周汇总通知
async function sendWeeklySummaryAlert(env: ExtendedEnv, summary: WeeklySummary) {
    const { start, end } = getLastWeekRange();
    const startDate = new Date(start).toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' });
    const endDate = new Date(end).toLocaleDateString('zh-CN', { timeZone: 'Asia/Shanghai' });

    const title = `【安卓】📊 周汇总报告 (${startDate} - ${endDate})`;

    let message = `汇总统计\n-------------\n`;
    message += `📈 新上架应用：${summary.totalNewApps} 个\n`;
    message += `📉 下架应用：${summary.totalTakenDown} 个\n`;
    message += `🔄 恢复上架应用：${summary.totalRestored} 个\n\n`;

    // 新上架应用详情
    if (summary.newApps.length > 0) {
        message += `新上架应用详情\n-------------\n`;
        summary.newApps.forEach((app, index) => {
            message += `${index + 1}. ${app.app_name}\n`;
            message += `   包名：${app.package_name}\n`;
            if (app.title && app.title !== app.app_name) {
                message += `   标题：${app.title}\n`;
            }
            if (app.released) {
                message += `   发布时间：${app.released}\n`;
            }
            message += `\n`;
        });
    }

    // 下架应用详情
    if (summary.takenDownApps.length > 0) {
        message += `下架应用详情\n-------------\n`;
        summary.takenDownApps.forEach((app, index) => {
            message += `${index + 1}. ${app.app_name}\n`;
            message += `   包名：${app.package_name}\n`;
            if (app.title && app.title !== app.app_name) {
                message += `   标题：${app.title}\n`;
            }
            if (app.released) {
                message += `   发布时间：${app.released}\n`;
            }
            if (app.downTime) {
                message += `   下架时间：${formatToBeiJingTime(app.downTime)}\n`;
            }
            if (app.daysOnline !== undefined) {
                message += `   在架天数：${app.daysOnline} 天\n`;
            }
            message += `\n`;
        });
    }

    // 恢复上架应用详情
    if (summary.restoredApps.length > 0) {
        message += `恢复上架应用详情\n-------------\n`;
        summary.restoredApps.forEach((app, index) => {
            message += `${index + 1}. ${app.app_name}\n`;
            message += `   包名：${app.package_name}\n`;
            if (app.title && app.title !== app.app_name) {
                message += `   标题：${app.title}\n`;
            }
            if (app.released) {
                message += `   发布时间：${app.released}\n`;
            }
            if (app.downTime) {
                message += `   下架时间：${formatToBeiJingTime(app.downTime)}\n`;
            }
            message += `\n`;
        });
    }

    // 如果没有任何变化
    if (summary.totalNewApps === 0 && summary.totalTakenDown === 0 && summary.totalRestored === 0) {
        message += `本周无应用状态变化 ✅\n`;
    }

    // 发送通知（使用一个虚拟的应用配置）
    const dummyApp: AppConfig = { package_name: 'weekly.summary', app_name: '周汇总' };
    await sendLarkAlert(env, dummyApp, message, title);
}

// 检查并发送周汇总（如果需要）
async function checkAndSendWeeklySummary(env: ExtendedEnv) {
    try {
        // 检查是否为周一早上9点
        if (!isMondayMorning9AM()) {
            return;
        }

        // 检查是否已经发送过本周的汇总
        const currentWeekKey = getCurrentWeekKey();
        const lastSentWeek = await env.APPS_STORE_ID_TMP.get('last_weekly_summary');

        if (lastSentWeek === currentWeekKey) {
            console.log('本周汇总已发送，跳过');
            return;
        }

        console.log('开始生成周汇总...');

        // 获取汇总数据
        const summary = await getWeeklySummary(env);

        // 发送汇总通知
        await sendWeeklySummaryAlert(env, summary);

        // 记录已发送
        await env.APPS_STORE_ID_TMP.put('last_weekly_summary', currentWeekKey);

        console.log('周汇总发送完成');

    } catch (error) {
        console.error('发送周汇总失败:', error);
    }
}

// 获取当前周的唯一标识（年份-周数）
function getCurrentWeekKey(): string {
    const now = new Date();
    const beijingTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));

    // 获取年份
    const year = beijingTime.getFullYear();

    // 计算周数（ISO 8601标准）
    const startOfYear = new Date(year, 0, 1);
    const dayOfYear = Math.floor((beijingTime.getTime() - startOfYear.getTime()) / (24 * 60 * 60 * 1000));
    const weekNumber = Math.ceil((dayOfYear + startOfYear.getDay() + 1) / 7);

    return `${year}-W${weekNumber}`;
}

async function sendStatusChangeAlert(
    env: ExtendedEnv,
    app: AppConfig,
    status: 'active' | 'taken_down' | 'new_active',
    details: PlayStoreResponse,
    downTime?: string | null
) {
    console.log(`[通知函数] 开始处理 ${app.app_name} 的 ${status} 状态通知`);
    console.log(`[通知函数] downTime参数: ${downTime}`);

    const action = status === 'active' ? '恢复上架' :
		status === 'taken_down' ? '下架' : '新上架';
    const emoji = status === 'active' ? '🎉' :
		status === 'taken_down' ? '❌' : '🆕';

    console.log(`[通知函数] 动作: ${action}, 表情: ${emoji}`);

    // 安全获取数据库记录
    let dbRecord: AppStatusRecord | null = null;
    try {
        const query = env.DB.prepare("SELECT * FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query);
        dbRecord = result.results?.[0] as AppStatusRecord || null;
    } catch (err) {
        console.error(`获取 ${app.package_name} 的数据库记录失败:`, err);
    }

    // 统一的安全字段获取函数
    const getField = (fieldName: string, maxLength: number, defaultValue: string = '未知') => {
        // 优先从数据库获取字段值（如果存在）
        if (dbRecord && (dbRecord as any)[fieldName]) {
            const value = (dbRecord as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        // 其次从API响应中获取
        if (details && (details as any)[fieldName]) {
            const value = (details as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        // 最后尝试从开发者对象获取
        if (details && details.developer && (details.developer as any)[fieldName]) {
            const value = (details.developer as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        return defaultValue;
    };

    // 构建完整的开发者信息部分
    const buildDeveloperInfo = () => {
        let info = `开发者信息\n-------------\n`;

        // 获取所有可用的开发者字段
        const devId = getField('devId', 100, '未知');
        const legalName = getField('developerLegalName', 255, '未知');

        info += `• 开发者ID: ${devId}\n`;
        info += `• 主体名称: ${legalName}\n`;

        return info;
    };

    // 构建标题，包含状态信息
    const customTitle = `【安卓】${emoji} ${app.app_name} - ${action}`;

    // 消息内容不再包含状态信息（已在标题中）
    let message = `应用信息\n-------------\n`;
    message += `名称：${app.app_name}\n`;
    message += `包名：${app.package_name}\n`;

    // 添加应用标题（如果可用）
    const title = getField('title', 255, '');
    if (title !== '') {
        message += `Google Play标题：${title}\n`;
    }

    message += `\n`;

    // 添加开发者信息
    message += buildDeveloperInfo();

	// 时间信息
	const released = getField('released', 50, '未知');
	if (status !== 'new_active') {
    	message += `\n时间信息\n-------------\n`;

		if (status === 'taken_down' && released) {
			message += `上架时间：${released}\n`;

			// 计算并显示在架天数
			if (released !== '未知' && downTime) {
				const releasedDate = parseReleasedDate(released);
				if (releasedDate) {
					const daysOnline = calculateDaysOnline(releasedDate, downTime);
					message += `在架天数：${daysOnline} 天\n`;
				}
			}
		} else if (status === 'active' && downTime) {
			message += `上架时间：${released}\n`;
			message += `上次下架：${formatToBeiJingTime(downTime)}\n`;
		}
	}

    // 添加Play Store链接（如果可用）
    const playStoreUrl = getField('playstoreUrl', 255, '');
    if (playStoreUrl !== '') {
        message += `\nPlay Store链接: ${playStoreUrl}\n`;
    }

    console.log(`[通知函数] 准备发送Lark消息给 ${app.app_name}`);
    console.log(`[通知函数] 消息标题: ${customTitle}`);
    console.log(`[通知函数] 消息内容长度: ${message.length} 字符`);

    await sendLarkAlert(env, app, message, customTitle);

    console.log(`[通知函数] ${app.app_name} 的Lark消息发送完成`);
}

async function checkPlayStore(apiKey: string, packageName: string, retries: number = 2): Promise<PlayStoreResponse> {
    const url = `https://playapi.yuandao.world/api/apps/${packageName}?country=id`;

    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            const res = await fetch(url, {
                method: 'GET',
                headers: { 'x-api-key': apiKey },
                // 添加超时控制
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            if (res.ok) {
                const data = await res.json();
                return {
                    status: 'available',
                    ...(data || {})
                };
            }

            if (res.status === 400) {
                return { status: 'unavailable' };
            }

            // 对于5xx错误进行重试
            if (res.status >= 500 && attempt < retries) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))); // 递增延迟
                continue;
            }

            throw new Error(`API请求失败: ${res.status} ${res.statusText}`);

        } catch (error) {
            if (attempt === retries) {
                throw error;
            }
            // 网络错误重试
            await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
    }

    throw new Error('API请求重试次数已用完');
}

async function sendLarkAlert(env: ExtendedEnv, app: AppConfig, message: string, customTitle?: string) {
    const webhookUrl = env.LARK_WEBHOOK_URL;

    // 使用自定义标题或默认标题
    const title = customTitle || `【🇮🇩 安卓】${app.app_name} 状态更新`;

    // 构建富文本消息
    const requestBody = {
        msg_type: "post",
        content: {
            post: {
                zh_cn: {
                    title: title,
                    content: [
                        [
                            {
                                tag: "text",
                                text: message
                            }
                        ]
                    ]
                }
            }
        }
    };

    console.log('Lark request URL:', webhookUrl);
    console.log('Lark request body:', JSON.stringify(requestBody, null, 2));

    try {
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        });

        const responseText = await response.text();
        if (!response.ok) {
            throw new Error(`Lark API error: ${response.status} ${response.statusText}\nResponse: ${responseText}`);
        }

        const result = JSON.parse(responseText);
        console.log('Lark message sent successfully:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Failed to send Lark message:', error);
        throw error;
    }
}
